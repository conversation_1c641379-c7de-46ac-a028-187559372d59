import 'package:flutter/material.dart';

class SummeryStudentSection extends StatelessWidget {
  const SummeryStudentSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.3),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Align(
            alignment: Alignment.topRight,
            child: const Text(
              'ملخص الأداء العام',
              style: TextStyle(
                color: Colors.black87,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(height: 20),
          // Chart area
          SizedBox(
            height: 200,
            child: Stack(
              children: [
                // Chart background
                Container(
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.grey.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                ),
                // Chart line
                CustomPaint(
                  size: const Size(double.infinity, 200),
                  painter: ChartPainter(),
                ),
                // Chart labels
                Positioned(
                  bottom: 10,
                  left: 0,
                  right: 0,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: const [
                      Text(
                        'يناير',
                        style: TextStyle(color: Colors.black54, fontSize: 12),
                      ),
                      Text(
                        'فبراير',
                        style: TextStyle(color: Colors.black54, fontSize: 12),
                      ),
                      Text(
                        'مارس',
                        style: TextStyle(color: Colors.black54, fontSize: 12),
                      ),
                      Text(
                        'أبريل',
                        style: TextStyle(color: Colors.black54, fontSize: 12),
                      ),
                      Text(
                        'مايو',
                        style: TextStyle(color: Colors.black54, fontSize: 12),
                      ),
                    ],
                  ),
                ),
                // Y-axis labels
                Positioned(
                  left: 10,
                  top: 0,
                  bottom: 30,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: const [
                      Text(
                        '100',
                        style: TextStyle(color: Colors.black54, fontSize: 10),
                      ),
                      Text(
                        '80',
                        style: TextStyle(color: Colors.black54, fontSize: 10),
                      ),
                      Text(
                        '60',
                        style: TextStyle(color: Colors.black54, fontSize: 10),
                      ),
                      Text(
                        '40',
                        style: TextStyle(color: Colors.black54, fontSize: 10),
                      ),
                      Text(
                        '20',
                        style: TextStyle(color: Colors.black54, fontSize: 10),
                      ),
                      Text(
                        '0',
                        style: TextStyle(color: Colors.black54, fontSize: 10),
                      ),
                    ],
                  ),
                ),
                // Tooltip
                Positioned(
                  top: 60,
                  left: 80,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black87,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'يناير',
                          style: TextStyle(color: Colors.white, fontSize: 12),
                        ),
                        Text(
                          'معدل التفاعل: 65',
                          style: TextStyle(color: Colors.white, fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class ChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = const Color.fromARGB(255, 74, 70, 193)
          ..strokeWidth = 3
          ..style = PaintingStyle.stroke;

    final path = Path();

    // Chart data points (representing months)
    final points = [
      Offset(size.width * 0.1, size.height * 0.8), // يناير - 20%
      Offset(size.width * 0.3, size.height * 0.7), // فبراير - 30%
      Offset(size.width * 0.5, size.height * 0.5), // مارس - 50%
      Offset(size.width * 0.7, size.height * 0.4), // أبريل - 60%
      Offset(size.width * 0.9, size.height * 0.2), // مايو - 80%
    ];

    // Draw the line
    path.moveTo(points[0].dx, points[0].dy);
    for (int i = 1; i < points.length; i++) {
      path.lineTo(points[i].dx, points[i].dy);
    }

    canvas.drawPath(path, paint);

    // Draw points
    final pointPaint =
        Paint()
          ..color = const Color.fromARGB(255, 246, 59, 59)
          ..style = PaintingStyle.fill;

    for (final point in points) {
      canvas.drawCircle(point, 4, pointPaint);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
