import 'package:flutter/material.dart';

PreferredSizeWidget? appBarTeatcher() => PreferredSize(
  preferredSize: const Size.fromHeight(80),
  child: Container(
    decoration: const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color.fromARGB(255, 74, 70, 193),
          Color.fromARGB(255, 246, 59, 59),
        ],
      ),
    ),
    child: Safe<PERSON><PERSON>(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            IconButton(
              onPressed: () {},
              icon: const Icon(
                Icons.notifications_outlined,
                color: Colors.white,
                size: 24,
              ),
            ),

            // Title
            const Text(
              'لوحة تحكم المعلم',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),

            IconButton(
              onPressed: () {},
              icon: const Icon(Icons.menu, color: Colors.white, size: 24),
            ),
          ],
        ),
      ),
    ),
  ),
);
