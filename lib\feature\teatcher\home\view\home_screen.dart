import 'package:al_7esa/core/widget/appbar_teacher_class.dart';
import 'package:al_7esa/core/widget/appbar_teatcher_home.dart';
import 'package:al_7esa/feature/teatcher/clacess/view/classes_Screen.dart';
import 'package:al_7esa/feature/teatcher/home/<USER>/widgets/home_screen_body.dart';
import 'package:al_7esa/feature/teatcher/notification/view/nofification_screen.dart';
import 'package:flutter/material.dart';

class TeactherHomeScreen extends StatefulWidget {
  const TeactherHomeScreen({super.key});

  @override
  State<TeactherHomeScreen> createState() => _TeactherHomeScreenState();
}

class _TeactherHomeScreenState extends State<TeactherHomeScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const HomeScreenBody(),
    const ClassroomsScreen(),
    const NotificationsScreen(),
    const AssignmentsScreen(),
    const ProfileScreen(),
  ];
  final List<PreferredSizeWidget?> appBarWidget = [
    appBarTeatcherHome(),
    appBarTeatcherClasses(),
    appBarTeatcherHome(),
    appBarTeatcherHome(),
    appBarTeatcherHome(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBarWidget[_currentIndex],
      body: _screens[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        backgroundColor: Colors.white,
        selectedItemColor: const Color.fromARGB(255, 74, 70, 193),
        unselectedItemColor: Colors.grey[600],
        selectedLabelStyle: const TextStyle(fontSize: 12),
        unselectedLabelStyle: const TextStyle(fontSize: 12),
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'الرئيسية'),
          BottomNavigationBarItem(icon: Icon(Icons.class_), label: 'الفصول'),
          BottomNavigationBarItem(
            icon: Icon(Icons.notifications),
            label: 'الإشعارات',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.subscriptions),
            label: 'اشتراكاتي',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'حسابي'),
        ],
      ),
    );
  }
}

// Placeholder screens for each tab



class AssignmentsScreen extends StatelessWidget {
  const AssignmentsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'اشتراكاتي',
        style: TextStyle(fontSize: 24, color: Colors.black87),
      ),
    );
  }
}

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'حسابي',
        style: TextStyle(fontSize: 24, color: Colors.black87),
      ),
    );
  }
}
