import 'package:al_7esa/core/spacing.dart';
import 'package:al_7esa/feature/teatcher/home/<USER>/widgets/activites_and_analysis.dart';
import 'package:al_7esa/feature/teatcher/home/<USER>/widgets/summery_student_section.dart';
import 'package:al_7esa/feature/teatcher/home/<USER>/widgets/variable_card.dart';
import 'package:flutter/material.dart';

class HomeScreenBody extends StatelessWidget {
  const HomeScreenBody({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: const Color.fromARGB(255, 200, 188, 185),
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Center(
                child: Text(
                  'مرحبًا بك في لوحة تحكم المعلم',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            vSpace(20),
            SizedBox(
              height: 350, // ارتفاع ثابت للسماح بالتمرير
              child: GridView.count(
                physics: const BouncingScrollPhysics(), // تمرير مرن
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.0, // نسبة العرض للارتفاع
                children: [
                  variableCard(
                    icon: Icons.group,
                    count: '0',
                    label: 'الطلاب',
                    gradientColors: const [
                      Color.fromARGB(255, 74, 70, 193),
                      Color.fromARGB(255, 246, 59, 59),
                    ],
                  ),
                  variableCard(
                    icon: Icons.laptop,
                    count: '0',
                    label: 'الفصول',
                    gradientColors: const [
                      Color.fromARGB(255, 74, 70, 193),
                      Color.fromARGB(255, 246, 59, 59),
                    ],
                  ),
                  variableCard(
                    icon: Icons.help_outline,
                    count: '0',
                    label: 'اختبارات نشطة',
                    gradientColors: const [
                      Color.fromARGB(255, 74, 70, 193),
                      Color.fromARGB(255, 246, 59, 59),
                    ],
                  ),
                  variableCard(
                    icon: Icons.list_alt,
                    count: '0',
                    label: 'واجبات نشطة',
                    gradientColors: const [
                      Color.fromARGB(255, 74, 70, 193),
                      Color.fromARGB(255, 246, 59, 59),
                    ],
                  ),
                ],
              ),
            ),
            vSpace(20),

            SummeryStudentSection(),
            vSpace(20),
            // Top students section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.3),
                    spreadRadius: 1,
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: const Color.fromARGB(255, 74, 70, 193),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: const [
                            Icon(
                              Icons.emoji_events,
                              color: Colors.white,
                              size: 16,
                            ),
                            SizedBox(width: 4),
                            Text(
                              'المتفوقون',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Spacer(),
                      const Text(
                        'أعلى الطلاب تحصيلاً',
                        style: TextStyle(
                          color: Colors.black87,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'أفضل 3 طلاب في الفصل',
                    style: TextStyle(color: Colors.black54, fontSize: 14),
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'لا توجد بيانات طلاب متاحة حالياً',
                    style: TextStyle(
                      color: Colors.black45,
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            vSpace(20),
            ActivitesAndAnalysis(),
          ],
        ),
      ),
    );
  }
}
