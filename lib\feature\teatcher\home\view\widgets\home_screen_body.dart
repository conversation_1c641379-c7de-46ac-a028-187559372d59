import 'package:al_7esa/core/spacing.dart';
import 'package:al_7esa/feature/teatcher/home/<USER>/widgets/variable_card.dart';
import 'package:flutter/material.dart';

class HomeScreenBody extends StatelessWidget {
  const HomeScreenBody({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Container(
            height: 200,
            decoration: BoxDecoration(
              color: const Color.fromARGB(255, 200, 188, 185),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Center(
              child: Text(
                'مرحبًا بك في لوحة تحكم المعلم',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          vSpace(20),
          GridView.count(
            shrinkWrap: true,
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            children: [
              variableCard(
                icon: Icons.group,
                count: '0',
                label: 'الطلاب',
                gradientColors: const [
                  Color.fromARGB(255, 74, 70, 193),
                  Color.fromARGB(255, 246, 59, 59),
                ],
              ),
              variableCard(
                icon: Icons.laptop,
                count: '0',
                label: 'الفصول',
                gradientColors: const [
                  Color.fromARGB(255, 74, 70, 193),
                  Color.fromARGB(255, 246, 59, 59),
                ],
              ),
              variableCard(
                icon: Icons.help_outline,
                count: '0',
                label: 'اختبارات نشطة',
                gradientColors: const [
                  Color.fromARGB(255, 74, 70, 193),
                  Color.fromARGB(255, 246, 59, 59),
                ],
              ),
              variableCard(
                icon: Icons.list_alt,
                count: '0',
                label: 'واجبات نشطة',
                gradientColors: const [
                  Color.fromARGB(255, 74, 70, 193),
                  Color.fromARGB(255, 246, 59, 59),
                ],
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Performance summary
        ],
      ),
    );
  }
}
