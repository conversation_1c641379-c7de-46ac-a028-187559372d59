import 'package:flutter/material.dart';

class HomeScreenBody extends StatelessWidget {
  const HomeScreenBody({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Social media section with hearts
          Container(
            height: 200,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Color(0xFF2E2E3E), Color(0xFF1A1A2E)],
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Stack(
              children: [
                // Social media icons and hearts
                Positioned(
                  top: 20,
                  left: 20,
                  child: _buildSocialIcon(Icons.facebook, Colors.blue),
                ),
                Positioned(top: 60, left: 60, child: _buildHeartIcon()),
                Positioned(
                  top: 40,
                  right: 30,
                  child: _buildSocialIcon(Icons.facebook, Colors.blue),
                ),
                Positioned(
                  bottom: 40,
                  left: 30,
                  child: _buildSocialIcon(Icons.facebook, Colors.blue),
                ),
                Positioned(bottom: 20, right: 40, child: _buildHeartIcon()),
                Positioned(top: 80, right: 80, child: _buildHeartIcon()),
                // Center text
                const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'يعتنا علي',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'سبوك',
                        style: TextStyle(color: Colors.white, fontSize: 18),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          // Statistics grid
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                _buildStatCard(
                  icon: Icons.group,
                  count: '0',
                  label: 'الطلاب',
                  color: const Color(0xFF4A46C1),
                ),
                _buildStatCard(
                  icon: Icons.laptop,
                  count: '0',
                  label: 'الفصول',
                  color: const Color(0xFF4A46C1),
                ),
                _buildStatCard(
                  icon: Icons.help_outline,
                  count: '0',
                  label: 'اختبارات نشطة',
                  color: const Color(0xFF4A46C1),
                ),
                _buildStatCard(
                  icon: Icons.list_alt,
                  count: '0',
                  label: 'واجبات نشطة',
                  color: const Color(0xFF4A46C1),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          // Performance summary
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF2E2E3E),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              'ملخص الأداء العام',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSocialIcon(IconData icon, Color color) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(color: color, shape: BoxShape.circle),
      child: Icon(icon, color: Colors.white, size: 24),
    );
  }

  Widget _buildHeartIcon() {
    return Container(
      width: 35,
      height: 35,
      decoration: const BoxDecoration(
        color: Colors.red,
        shape: BoxShape.circle,
      ),
      child: const Icon(Icons.favorite, color: Colors.white, size: 20),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String count,
    required String label,
    required Color color,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF2E2E3E),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(color: color, shape: BoxShape.circle),
            child: Icon(icon, color: Colors.white, size: 30),
          ),
          const SizedBox(height: 12),
          Text(
            count,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 32,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(color: Colors.white70, fontSize: 14),
          ),
        ],
      ),
    );
  }
}
