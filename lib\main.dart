import 'package:al_7esa/feature/teatcher/home/<USER>/home_screen.dart';
import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'al-7esa',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color.fromARGB(255, 74, 70, 193),
          brightness: Brightness.dark,
        ),
        scaffoldBackgroundColor: const Color(0xff141412),
        useMaterial3: true,
      ),
      home: const TeactherHomeScreen(),
    );
  }
}
